# JupyMCP

JupyMCP is a Model Context Protocol (MCP) server for Jupyter Notebooks that provides both tools and resources for comprehensive notebook interaction.

## Usage

```json
{
    "mcpServers": {
        "jupymcp": {
            "command": "uvx",
            "args": ["jupymcp",
                "--server-url", "http://localhost:8888",
                "--token", "<token>",
                "--path", "<path>"]
        }
    }
}
```

```python
from mcp.client.stdio import StdioServerParameters
from swarmx import Swarm

swarm = Swarm(
    jupyter=StdioServerParameters(
        command="uvx",
        args=["jupymcp", "--server-url", "http://localhost:8890", "--token", "MY_TOKEN", "--path", "main.ipynb"],
    )
)
```

## Alternatives

- [jupyter-mcp-server](https://github.com/datalayer/jupyter-mcp-server)
- [jupyter-mcp](https://pypi.org/project/jupyter-mcp/)
- [mcp-jupyter](https://pypi.org/project/mcp-jupyter/)

## Features

JupyMCP provides comprehensive Jupyter Notebook integration through MCP:

### Tools
- **Cell Execution**: Execute, add, and modify notebook cells
- **Cell Management**: Add, insert, and manage code, markdown, and raw cells
- **Metadata Operations**: Get and set cell and notebook metadata

### Resources
- **Kernel Information**: Access kernel status, ID, and capabilities via `jupyter://kernel/info`
- **Notebook Metadata**: Retrieve notebook-level metadata via `jupyter://notebook/metadata`
- **Notebook Structure**: Get overview of all cells and types via `jupyter://notebook/structure`
- **Cell Content**: Access individual cell source code via `jupyter://cell/{index}/source`
- **Cell Metadata**: Access individual cell metadata via `jupyter://cell/{index}/metadata`
- **All Cells**: Retrieve complete notebook content via `jupyter://cells/all`
- **Status Information**: Check connection status via `jupyter://status`

## Why yet another one?

I personally want a full-featured Jupyter Notebook server that can be used as a MCP server.
All of the above alternatives are either not meeting my requirements (e.g. lack of editing).

## Why not a folk of one of the above?

I think it's better to start from scratch with LLM assistance. LLM-driven bootstrap is fun.

## MCP Resource URIs

JupyMCP exposes the following MCP resources:

| Resource URI | Description | Parameters |
|--------------|-------------|------------|
| `jupyter://kernel/info` | Kernel information and status | None |
| `jupyter://notebook/metadata` | Notebook metadata | None |
| `jupyter://notebook/structure` | Notebook structure overview | None |
| `jupyter://cell/{index}/source` | Individual cell source code | `index`: Cell index (0-based) |
| `jupyter://cell/{index}/metadata` | Individual cell metadata | `index`: Cell index (0-based) |
| `jupyter://cells/all` | All cells with content and metadata | None |
| `jupyter://status` | Overall connection status | None |

## Example Resource Usage

```python
# Using with MCP client
from mcp.client.stdio import StdioServerParameters
from swarmx import Swarm

swarm = Swarm(
    jupyter=StdioServerParameters(
        command="uvx",
        args=["jupymcp", "--server-url", "http://localhost:8890", "--token", "MY_TOKEN", "--path", "main.ipynb"],
    )
)

# Access resources
kernel_info = swarm.jupyter.read_resource("jupyter://kernel/info")
notebook_structure = swarm.jupyter.read_resource("jupyter://notebook/structure")
cell_source = swarm.jupyter.read_resource("jupyter://cell/0/source")
```

## Roadmap

- [x] MCP Resource support for kernel and notebook
- [ ] Multiple Kernel support
- [ ] Multiple Notebook support
- [ ] Notebook import/export
